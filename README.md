# 身份提供商 (Identity Provider - IdP)

一个现代化的身份提供商系统，提供统一身份认证和授权服务，支持多种认证方式和SSO协议。

## ✨ 特性

- 🔐 **多因素认证(MFA)**: 支持TOTP、邮件验证码、短信验证码
- 🌐 **联合身份认证**: 集成Google、GitHub等第三方OAuth提供商
- 🔑 **单点登录(SSO)**: 支持OpenID Connect、OAuth 2.0、SAML 2.0
- 🛡️ **零信任模式**: 基于风险评估的自适应认证
- 👥 **用户管理**: 完整的用户生命周期管理
- 📊 **审计日志**: 全面的安全事件记录
- 🚀 **高性能**: 基于Node.js和PostgreSQL构建
- 🔒 **安全优先**: 遵循最佳安全实践

## 🏗️ 技术栈

- **后端**: Node.js + TypeScript + Express.js
- **数据库**: PostgreSQL + Prisma ORM
- **认证**: JWT + Passport.js
- **MFA**: Speakeasy (TOTP) + Nodemailer + Twilio
- **测试**: Jest + Supertest
- **日志**: Winston
- **验证**: Joi

## 🚀 快速开始

### 环境要求

- Node.js 18.0+
- PostgreSQL 13+
- npm 或 yarn

### 安装

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd id-provider
   ```

2. **运行设置脚本**
   ```bash
   chmod +x scripts/setup.sh
   ./scripts/setup.sh
   ```

3. **配置环境变量**

   编辑 `.env` 文件，配置数据库连接和其他必需参数：
   ```bash
   # 数据库配置
   DATABASE_URL="postgresql://username:password@localhost:5432/id_provider"

   # JWT密钥
   JWT_SECRET="your-super-secret-jwt-key"
   JWT_REFRESH_SECRET="your-super-secret-refresh-key"

   # 邮件配置
   SMTP_HOST="smtp.gmail.com"
   SMTP_USER="<EMAIL>"
   SMTP_PASS="your-app-password"
   ```

4. **初始化数据库**
   ```bash
   # 创建数据库
   createdb id_provider

   # 运行迁移
   npm run db:migrate

   # 初始化种子数据
   npm run db:seed
   ```

5. **启动服务**
   ```bash
   # 开发模式
   npm run dev

   # 生产模式
   npm run build
   npm start
   ```

服务将在 `http://localhost:3000` 启动。

### 默认账户

- **管理员账户**: <EMAIL> / admin123456

## 📖 API 文档

### 认证相关

- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/logout` - 用户登出
- `POST /api/v1/auth/refresh-token` - 刷新令牌
- `POST /api/v1/auth/forgot-password` - 忘记密码
- `POST /api/v1/auth/reset-password` - 重置密码

### 用户管理

- `GET /api/v1/me` - 获取当前用户信息
- `PUT /api/v1/me` - 更新用户资料
- `GET /api/v1/me/sessions` - 获取用户会话
- `DELETE /api/v1/me/sessions/:id` - 终止会话

### 多因素认证

- `GET /api/v1/me/mfa` - 获取MFA状态
- `POST /api/v1/me/mfa/enable` - 启用MFA
- `POST /api/v1/me/mfa/verify` - 验证MFA
- `DELETE /api/v1/me/mfa/devices/:id` - 禁用MFA设备

详细的API文档请查看 [docs/api.md](docs/api.md)。

## 🧪 测试

```bash
# 运行所有测试
npm test

# 运行测试并生成覆盖率报告
npm run test:coverage

# 监听模式运行测试
npm run test:watch
```

## 📚 文档

- [用户需求文档](docs/user_requirements.md)
- [系统架构文档](docs/architecture.md)
- [数据库设计文档](docs/database.md)
- [MFA功能文档](docs/mfa.md)
- [开发指南](docs/development.md)

## 🔧 开发

### 项目结构

```
src/
├── config/          # 配置文件
├── controllers/     # 控制器层
├── middleware/      # 中间件
├── models/          # 数据模型
├── routes/          # 路由定义
├── services/        # 业务逻辑层
├── utils/           # 工具函数
├── types/           # TypeScript类型定义
└── test/            # 测试文件
```

### 开发命令

```bash
npm run dev          # 启动开发服务器
npm run build        # 构建项目
npm run lint         # 代码检查
npm run lint:fix     # 自动修复代码问题
npm run db:migrate   # 运行数据库迁移
npm run db:generate  # 生成Prisma客户端
npm run db:studio    # 打开Prisma Studio
```

## 🐳 Docker 部署

```bash
# 构建镜像
docker build -t id-provider .

# 运行容器
docker run -p 3000:3000 --env-file .env id-provider

# 使用 docker-compose
docker-compose up -d
```

## 🤝 贡献

欢迎贡献代码！请查看 [开发指南](docs/development.md) 了解详细信息。

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如果您遇到问题或有疑问，请：

1. 查看 [文档](docs/)
2. 搜索 [Issues](../../issues)
3. 创建新的 [Issue](../../issues/new)

## 🗺️ 路线图

- [ ] 联合身份认证实现
- [ ] SSO协议支持
- [ ] 零信任模式
- [ ] 前端管理界面
- [ ] 移动应用支持
- [ ] 高级审计功能
- [ ] 性能优化
- [ ] 国际化支持