// Prisma schema file for IdP (Identity Provider)
// 身份提供商数据库模式定义

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 用户表 - 核心用户信息
model User {
  id                String    @id @default(uuid())
  email             String    @unique
  phone             String?   @unique
  username          String?   @unique
  passwordHash      String?   // 可为空，支持仅第三方登录的用户
  nickname          String?
  firstName         String?
  lastName          String?
  avatar            String?
  emailVerified     Boolean   @default(false)
  phoneVerified     Boolean   @default(false)
  isActive          Boolean   @default(true)
  isLocked          Boolean   @default(false)
  lockReason        String?
  lastLoginAt       DateTime?
  lastLoginIp       String?
  passwordChangedAt DateTime?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // 关联关系
  sessions          Session[]
  mfaDevices        MFADevice[]
  federatedIdentities FederatedIdentity[]
  auditLogs         AuditLog[]
  riskAssessments   RiskAssessment[]
  userRoles         UserRole[]
  refreshTokens     RefreshToken[]
  authorizationCodes AuthorizationCode[]
  passwordResetTokens PasswordResetToken[]
  emailVerificationTokens EmailVerificationToken[]
  permissionUsageLogs PermissionUsageLog[]
  delegatedPermissions PermissionDelegation[] @relation("DelegatorRelation")
  receivedDelegations PermissionDelegation[] @relation("DelegateeRelation")

  @@map("users")
}

// 应用表 - 注册的服务提供商
model Application {
  id                String    @id @default(uuid())
  name              String
  description       String?
  clientId          String    @unique
  clientSecret      String
  redirectUris      Json      // JSON数组存储多个回调URL
  allowedOrigins    Json      // 允许的CORS源
  logoUrl           String?
  homepageUrl       String?
  privacyPolicyUrl  String?
  termsOfServiceUrl String?

  // SSO协议配置
  supportedProtocols Json     // ["oidc", "oauth2", "saml", "custom-oauth", "api-key", "webhook"]
  oidcConfig        Json?     // OIDC特定配置
  samlConfig        Json?     // SAML特定配置
  customProtocolConfig Json?  // 自定义协议配置

  // 非标准应用支持
  appType           String    @default("standard") // "standard", "legacy", "custom", "api_only", "webhook", "mobile", "iot"
  customAuthFlow    Json?     // 自定义认证流程配置

  // 钩子函数配置
  preAuthHook       String?   // 预认证钩子函数
  postAuthHook      String?   // 后认证钩子函数
  tokenTransformHook String?  // 令牌转换钩子函数
  userTransformHook String?   // 用户信息转换钩子函数

  // Webhook配置
  webhookUrls       Json?     // Webhook URL配置 {"onSuccess": "url", "onError": "url", "onLogout": "url"}
  webhookSecret     String?   // Webhook签名密钥
  webhookTimeout    Int?      @default(30) // Webhook超时时间（秒）

  // API配置
  apiKeyEnabled     Boolean   @default(false)
  apiKeyConfig      Json?     // API密钥配置
  rateLimitConfig   Json?     // 速率限制配置

  // 安全配置
  requireMfa        Boolean   @default(false)
  zeroTrustEnabled  Boolean   @default(false)
  allowedIpRanges   Json?     // 允许的IP范围
  tokenLifetime     Int?      @default(3600) // 令牌生命周期（秒）

  // 扩展配置
  customSettings    Json?     // 自定义设置
  pluginConfig      Json?     // 插件配置

  isActive          Boolean   @default(true)
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // 关联关系
  sessions          Session[]
  auditLogs         AuditLog[]
  protocolConfigs   ApplicationProtocolConfig[]
  apiKeys           ApiKey[]
  oauthClients      OAuthClient[]
  applicationPermissions ApplicationPermission[]
  permissionUsageLogs PermissionUsageLog[]
  permissionDelegations PermissionDelegation[]

  @@map("applications")
}

// 会话表 - 用户登录会话
model Session {
  id              String    @id @default(uuid())
  userId          String
  applicationId   String?   // 可为空，表示IdP自身的会话
  sessionToken    String    @unique
  deviceId        String?   // 设备标识
  deviceInfo      Json?     // 设备信息
  ipAddress       String
  userAgent       String?
  location        Json?     // 地理位置信息
  
  // 会话状态
  isActive        Boolean   @default(true)
  expiresAt       DateTime
  lastAccessedAt  DateTime  @default(now())
  
  // 认证信息
  authMethod      String    // "password", "mfa", "federated"
  mfaVerified     Boolean   @default(false)
  riskScore       Int?      // 风险评分 0-100
  
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  
  // 关联关系
  user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  application     Application? @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  
  @@map("sessions")
}

// MFA设备表 - 多因素认证设备
model MFADevice {
  id            String    @id @default(uuid())
  userId        String
  type          String    // "totp", "email", "sms"
  name          String    // 用户自定义设备名称
  secret        String?   // TOTP密钥（加密存储）
  backupCodes   Json?     // 备用恢复码（加密存储）
  phoneNumber   String?   // 短信MFA的手机号
  emailAddress  String?   // 邮件MFA的邮箱
  
  isActive      Boolean   @default(true)
  isVerified    Boolean   @default(false)
  lastUsedAt    DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // 关联关系
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("mfa_devices")
}

// 联合身份表 - 第三方账户关联
model FederatedIdentity {
  id            String    @id @default(uuid())
  userId        String
  provider      String    // "google", "github", "microsoft"
  providerId    String    // 第三方平台的用户ID
  email         String?   // 第三方账户邮箱
  name          String?   // 第三方账户名称
  avatar        String?   // 第三方账户头像
  accessToken   String?   // 访问令牌（加密存储）
  refreshToken  String?   // 刷新令牌（加密存储）
  expiresAt     DateTime?
  
  isActive      Boolean   @default(true)
  lastUsedAt    DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // 关联关系
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([provider, providerId])
  @@map("federated_identities")
}

// 审计日志表 - 安全和操作日志
model AuditLog {
  id            String    @id @default(uuid())
  userId        String?   // 可为空，系统操作
  applicationId String?   // 可为空，IdP自身操作
  action        String    // 操作类型
  resource      String    // 操作资源
  details       Json?     // 详细信息
  ipAddress     String?
  userAgent     String?
  success       Boolean
  errorMessage  String?
  
  createdAt     DateTime  @default(now())
  
  // 关联关系
  user          User?     @relation(fields: [userId], references: [id], onDelete: SetNull)
  application   Application? @relation(fields: [applicationId], references: [id], onDelete: SetNull)
  
  @@map("audit_logs")
}

// 风险评估表 - 零信任模式风险评估
model RiskAssessment {
  id            String    @id @default(uuid())
  userId        String
  sessionId     String?
  
  // 风险因子
  ipRisk        Int       @default(0)    // IP风险评分
  deviceRisk    Int       @default(0)    // 设备风险评分
  behaviorRisk  Int       @default(0)    // 行为风险评分
  locationRisk  Int       @default(0)    // 位置风险评分
  timeRisk      Int       @default(0)    // 时间风险评分
  
  totalRisk     Int       @default(0)    // 总风险评分
  riskLevel     String    // "low", "medium", "high", "critical"
  
  // 评估结果
  action        String    // "allow", "mfa_required", "deny"
  reason        String?   // 风险原因
  
  createdAt     DateTime  @default(now())
  
  // 关联关系
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("risk_assessments")
}

// 权限定义表 - 标准化权限元数据
model Permission {
  id                  String    @id @default(uuid())
  name                String
  description         String?
  category            String
  resourceType        String    @map("resource_type")
  operations          Json      // 支持的操作数组
  level               Int       @default(1) // 权限级别 1-10
  dependencies        Json?     // 权限依赖关系
  constraints         Json?     // 权限约束条件
  tags                Json?     // 权限标签
  isSensitive         Boolean   @default(false) @map("is_sensitive")
  isDelegatable       Boolean   @default(true) @map("is_delegatable")
  validity            Json?     // 权限有效期配置

  // 元数据字段
  createdBy           String    @map("created_by")
  createdAt           DateTime  @default(now()) @map("created_at")
  updatedBy           String    @map("updated_by")
  updatedAt           DateTime  @updatedAt @map("updated_at")
  version             String    @default("1.0.0")
  sourceApplication   String?   @map("source_application")
  documentationUrl    String?   @map("documentation_url")
  owner               String?
  approvalStatus      String    @default("pending") @map("approval_status") // pending, approved, rejected, deprecated

  // 关联关系
  applicationPermissions ApplicationPermission[]
  usageLogs           PermissionUsageLog[]
  changeRecords       PermissionChangeRecord[]
  delegations         PermissionDelegation[]

  @@map("permissions")
}

// 应用权限关联表
model ApplicationPermission {
  id                  String    @id @default(uuid())
  applicationId       String    @map("application_id")
  permissionId        String    @map("permission_id")
  isRequired          Boolean   @default(false) @map("is_required")
  grantedAt           DateTime? @map("granted_at")
  expiresAt           DateTime? @map("expires_at")
  grantedBy           String?   @map("granted_by")
  revokedAt           DateTime? @map("revoked_at")
  revokedBy           String?   @map("revoked_by")
  revokeReason        String?   @map("revoke_reason")

  // 权限配置
  customConstraints   Json?     @map("custom_constraints")
  delegationAllowed   Boolean   @default(true) @map("delegation_allowed")
  maxDelegationDepth  Int       @default(3) @map("max_delegation_depth")

  // 元数据
  createdAt           DateTime  @default(now()) @map("created_at")
  updatedAt           DateTime  @updatedAt @map("updated_at")

  // 关联关系
  application         Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  permission          Permission  @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([applicationId, permissionId], name: "unique_app_permission")
  @@map("application_permissions")
}

// 权限使用日志表
model PermissionUsageLog {
  id                  String    @id @default(uuid())
  userId              String    @map("user_id")
  applicationId       String?   @map("application_id")
  permissionId        String    @map("permission_id")
  resourceId          String?   @map("resource_id")
  resourceType        String?   @map("resource_type")
  action              String
  result              String    // granted, denied, error

  // 请求信息
  ipAddress           String?   @map("ip_address")
  userAgent           String?   @map("user_agent")
  deviceType          String?   @map("device_type")
  location            Json?
  sessionId           String?   @map("session_id")

  // 验证详情
  validationTimeMs    Int?      @map("validation_time_ms")
  failedConstraints   Json?     @map("failed_constraints")
  missingDependencies Json?     @map("missing_dependencies")

  // 额外信息
  requestDetails      Json?     @map("request_details")
  responseDetails     Json?     @map("response_details")

  // 时间戳
  createdAt           DateTime  @default(now()) @map("created_at")

  // 关联关系
  user                User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  application         Application? @relation(fields: [applicationId], references: [id], onDelete: SetNull)
  permission          Permission  @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@map("permission_usage_logs")
}

// 权限变更记录表
model PermissionChangeRecord {
  id                  String    @id @default(uuid())
  permissionId        String    @map("permission_id")
  changeType          String    @map("change_type") // created, updated, deleted, activated, deactivated
  previousValue       Json?     @map("previous_value")
  newValue            Json?     @map("new_value")
  reason              String

  // 变更者信息
  changedBy           String    @map("changed_by")
  changedAt           DateTime  @default(now()) @map("changed_at")

  // 审批信息
  approvalRequired    Boolean   @default(false) @map("approval_required")
  approvalStatus      String    @default("approved") @map("approval_status") // pending, approved, rejected
  approvedBy          String?   @map("approved_by")
  approvedAt          DateTime? @map("approved_at")
  approvalComments    String?   @map("approval_comments")

  // 关联关系
  permission          Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@map("permission_change_records")
}

// 权限模板表
model PermissionTemplate {
  id                  String    @id @default(uuid())
  name                String
  description         String?
  category            String
  permissions         Json      // 权限ID数组
  variables           Json?     // 模板变量
  isSystemTemplate    Boolean   @default(false) @map("is_system_template")

  // 元数据
  createdBy           String    @map("created_by")
  createdAt           DateTime  @default(now()) @map("created_at")
  updatedAt           DateTime  @updatedAt @map("updated_at")

  @@map("permission_templates")
}

// 权限委托记录表
model PermissionDelegation {
  id                  String    @id @default(uuid())
  delegatorId         String    @map("delegator_id")
  delegateeId         String    @map("delegatee_id")
  permissionId        String    @map("permission_id")
  applicationId       String?   @map("application_id")

  // 委托配置
  delegationLevel     Int       @default(1) @map("delegation_level")
  maxDelegationDepth  Int       @default(1) @map("max_delegation_depth")
  canRedelegate       Boolean   @default(false) @map("can_redelegate")

  // 有效期
  grantedAt           DateTime  @default(now()) @map("granted_at")
  expiresAt           DateTime? @map("expires_at")
  revokedAt           DateTime? @map("revoked_at")
  revokedBy           String?   @map("revoked_by")
  revokeReason        String?   @map("revoke_reason")

  // 约束条件
  constraints         Json?

  // 关联关系
  delegator           User        @relation("DelegatorRelation", fields: [delegatorId], references: [id], onDelete: Cascade)
  delegatee           User        @relation("DelegateeRelation", fields: [delegateeId], references: [id], onDelete: Cascade)
  permission          Permission  @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  application         Application? @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@unique([delegatorId, delegateeId, permissionId, applicationId], name: "unique_delegation")
  @@map("permission_delegations")
}

// 角色表 - 用户角色管理
model Role {
  id            String    @id @default(uuid())
  name          String    @unique
  description   String?
  permissions   Json?     // 权限列表
  
  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // 关联关系
  userRoles     UserRole[]
  
  @@map("roles")
}

// 用户角色关联表
model UserRole {
  id        String    @id @default(uuid())
  userId    String
  roleId    String
  
  createdAt DateTime  @default(now())
  
  // 关联关系
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  role      Role      @relation(fields: [roleId], references: [id], onDelete: Cascade)
  
  @@unique([userId, roleId])
  @@map("user_roles")
}

// 刷新令牌表 - JWT刷新令牌管理
model RefreshToken {
  id            String    @id @default(uuid())
  userId        String
  token         String    @unique
  deviceId      String?
  
  isActive      Boolean   @default(true)
  expiresAt     DateTime
  createdAt     DateTime  @default(now())
  
  // 关联关系
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("refresh_tokens")
}

// 应用协议配置表 - 应用特定的协议配置
model ApplicationProtocolConfig {
  id              String    @id @default(uuid())
  applicationId   String
  protocolName    String    // "oidc", "saml", "custom-oauth", etc.
  protocolVersion String    @default("1.0")
  config          Json      // 协议特定配置
  isActive        Boolean   @default(true)

  // 自定义处理器
  customHandlers  Json?     // {"preAuth": "handler_name", "postAuth": "handler_name"}

  // Webhook配置
  webhooks        Json?     // {"onSuccess": "url", "onError": "url"}

  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // 关联关系
  application     Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@unique([applicationId, protocolName])
  @@map("application_protocol_configs")
}

// 插件表 - 管理认证插件
model Plugin {
  id            String    @id @default(uuid())
  name          String    @unique
  version       String
  description   String?
  author        String?

  // 插件文件信息
  filePath      String    // 插件文件路径
  entryPoint    String    // 入口点函数
  checksum      String    // 文件校验和

  // 插件配置
  config        Json?     // 插件配置
  dependencies  Json?     // 依赖的其他插件

  // 插件状态
  isEnabled     Boolean   @default(false)
  isLoaded      Boolean   @default(false)
  loadError     String?   // 加载错误信息

  // 插件提供的功能
  protocolAdapters Json?    // 提供的协议适配器
  customHandlers   Json?    // 提供的自定义处理器

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@map("plugins")
}

// 自定义认证流程表
model CustomAuthFlow {
  id            String    @id @default(uuid())
  name          String    @unique
  description   String?

  // 流程定义
  steps         Json      // 认证流程步骤定义
  config        Json?     // 流程配置

  // 使用统计
  usageCount    Int       @default(0)
  lastUsedAt    DateTime?

  isActive      Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@map("custom_auth_flows")
}

// API密钥表 - 管理应用API密钥
model ApiKey {
  id            String    @id @default(uuid())
  applicationId String
  name          String    // 密钥名称
  keyHash       String    @unique // 密钥哈希值
  keyPrefix     String    // 密钥前缀（用于识别）

  // 权限配置
  scopes        Json?     // 权限范围
  allowedIps    Json?     // 允许的IP地址

  // 使用统计
  usageCount    Int       @default(0)
  lastUsedAt    DateTime?

  // 状态
  isActive      Boolean   @default(true)
  expiresAt     DateTime?

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // 关联关系
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}

// 系统配置表 - 全局配置
model SystemConfig {
  id            String    @id @default(uuid())
  key           String    @unique
  value         Json
  description   String?

  updatedAt     DateTime  @updatedAt

  @@map("system_configs")
}

// OAuth客户端表 - OIDC客户端注册
model OAuthClient {
  id                    String    @id @default(uuid())
  applicationId         String?   // 关联的应用ID（可为空，支持独立OAuth客户端）
  clientId              String    @unique
  clientSecret          String
  name                  String
  description           String?

  // 重定向URI和权限配置
  redirectUris          Json      // 允许的重定向URI数组
  grantTypes            Json      // 支持的授权类型 ["authorization_code", "refresh_token", "client_credentials"]
  responseTypes         Json      // 支持的响应类型 ["code", "token", "id_token"]
  scopes                Json      // 允许的权限范围 ["openid", "profile", "email", "offline_access"]

  // 客户端配置
  tokenEndpointAuthMethod String  @default("client_secret_basic") // "client_secret_basic", "client_secret_post", "private_key_jwt"
  requirePkce           Boolean   @default(false)
  requireConsent        Boolean   @default(true)

  // 令牌配置
  accessTokenLifetime   Int       @default(3600)    // 访问令牌生命周期（秒）
  refreshTokenLifetime  Int       @default(2592000) // 刷新令牌生命周期（秒，默认30天）
  idTokenLifetime       Int       @default(3600)    // ID令牌生命周期（秒）

  // 客户端元数据
  logoUri               String?
  clientUri             String?
  policyUri             String?
  tosUri                String?

  isActive              Boolean   @default(true)
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  // 关联关系
  application           Application? @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  authorizationCodes    AuthorizationCode[]

  @@map("oauth_clients")
}

// 授权码表 - OAuth授权码存储
model AuthorizationCode {
  id                    String    @id @default(uuid())
  code                  String    @unique
  clientId              String
  userId                String
  redirectUri           String
  scopes                String    // 空格分隔的权限范围

  // PKCE支持
  codeChallenge         String?
  codeChallengeMethod   String?   // "plain" or "S256"

  // OpenID Connect参数
  nonce                 String?   // 用于ID令牌

  // 状态和过期
  used                  Boolean   @default(false)
  expiresAt             DateTime
  createdAt             DateTime  @default(now())

  // 关联关系
  client                OAuthClient @relation(fields: [clientId], references: [clientId], onDelete: Cascade)
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("authorization_codes")
}

// 密码重置令牌表
model PasswordResetToken {
  id            String    @id @default(uuid())
  token         String    @unique
  userId        String
  used          Boolean   @default(false)
  expiresAt     DateTime
  createdAt     DateTime  @default(now())

  // 关联关系
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("password_reset_tokens")
}

// 邮箱验证令牌表
model EmailVerificationToken {
  id            String    @id @default(uuid())
  token         String    @unique
  userId        String
  email         String    // 要验证的邮箱
  used          Boolean   @default(false)
  expiresAt     DateTime
  createdAt     DateTime  @default(now())

  // 关联关系
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("email_verification_tokens")
}

// 协议适配器表 - 非标准应用支持
model ProtocolAdapter {
  id            String    @id @default(uuid())
  name          String    // 适配器名称
  version       String    // 适配器版本
  config        Json      // 适配器配置
  isActive      Boolean   @default(true)
  usageCount    Int       @default(0)

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  lastUsed      DateTime?

  @@map("protocol_adapters")
}
