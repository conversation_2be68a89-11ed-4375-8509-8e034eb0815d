/**
 * Redis配置
 * 管理Redis连接和配置选项
 */

import { config } from './index';

export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  keyPrefix: string;
  retryDelayOnFailover: number;
  maxRetriesPerRequest: number;
  lazyConnect: boolean;
  keepAlive: number;
  family: number;
  connectTimeout: number;
  commandTimeout: number;
}

/**
 * Redis连接配置
 */
export const redisConfig: RedisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB || '0'),
  keyPrefix: process.env.REDIS_KEY_PREFIX || 'idp:',
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  keepAlive: 30000,
  family: 4,
  connectTimeout: 10000,
  commandTimeout: 5000
};

/**
 * Redis键命名规范
 */
export const RedisKeys = {
  // 用户会话
  SESSION: (sessionId: string) => `session:${sessionId}`,
  USER_SESSIONS: (userId: string) => `user:${userId}:sessions`,
  
  // JWT令牌管理
  JWT_BLACKLIST: (jti: string) => `jwt:blacklist:${jti}`,
  REFRESH_TOKEN: (tokenId: string) => `refresh:${tokenId}`,
  
  // 速率限制
  RATE_LIMIT: (key: string) => `rate:${key}`,
  RATE_LIMIT_LOGIN: (ip: string) => `rate:login:${ip}`,
  RATE_LIMIT_REGISTER: (ip: string) => `rate:register:${ip}`,
  RATE_LIMIT_PASSWORD_RESET: (ip: string, email: string) => `rate:password:${ip}:${email}`,
  
  // OAuth状态
  OAUTH_STATE: (state: string) => `oauth:state:${state}`,
  OAUTH_CODE: (code: string) => `oauth:code:${code}`,
  
  // MFA验证码
  MFA_CODE: (userId: string, type: string) => `mfa:${type}:${userId}`,
  MFA_BACKUP_CODES: (userId: string) => `mfa:backup:${userId}`,
  
  // 用户权限缓存
  USER_PERMISSIONS: (userId: string) => `user:${userId}:permissions`,
  USER_PROFILE: (userId: string) => `user:${userId}:profile`,
  
  // 系统配置缓存
  SYSTEM_CONFIG: () => 'system:config',
  OAUTH_PROVIDERS: () => 'oauth:providers',
  
  // 安全相关
  FAILED_LOGIN_ATTEMPTS: (ip: string) => `security:failed:${ip}`,
  SUSPICIOUS_ACTIVITY: (ip: string) => `security:suspicious:${ip}`,
  
  // 邮件验证
  EMAIL_VERIFICATION: (token: string) => `email:verify:${token}`,
  PASSWORD_RESET: (token: string) => `password:reset:${token}`,
  
  // 统计数据
  STATS_DAILY_LOGINS: (date: string) => `stats:logins:${date}`,
  STATS_DAILY_REGISTRATIONS: (date: string) => `stats:registrations:${date}`,
  
  // 缓存锁
  LOCK: (resource: string) => `lock:${resource}`,

  // 设备信息
  DEVICE_INFO: (deviceId: string) => `device:${deviceId}`,

  // 会话事件
  SESSION_EVENT: (sessionId: string, event: string) => `session:${sessionId}:event:${event}`,

  // 用户黑名单索引
  USER_BLACKLIST_INDEX: (userId: string) => `blacklist:user:${userId}`,

  // 黑名单原因索引
  BLACKLIST_REASON_INDEX: (reason: string) => `blacklist:reason:${reason}`,

  // 黑名单统计
  BLACKLIST_STATS: () => `blacklist:stats`,
  BLACKLIST_DAILY_STATS: (date: string) => `blacklist:stats:${date}`,

  // 速率限制
  RATE_LIMIT_BUCKET: (key: string) => `ratelimit:bucket:${key}`,
  RATE_LIMIT_WINDOW: (key: string, window: number | string) => `ratelimit:window:${key}:${window}`,
  RATE_LIMIT_SLIDING: (key: string) => `ratelimit:sliding:${key}`
} as const;

/**
 * Redis缓存过期时间配置（秒）
 */
export const RedisTTL = {
  // 会话相关
  SESSION: 24 * 60 * 60, // 24小时
  USER_SESSIONS: 7 * 24 * 60 * 60, // 7天
  
  // JWT相关
  JWT_BLACKLIST: 15 * 60, // 15分钟（访问令牌过期时间）
  REFRESH_TOKEN: 7 * 24 * 60 * 60, // 7天
  
  // 速率限制
  RATE_LIMIT_LOGIN: 15 * 60, // 15分钟
  RATE_LIMIT_REGISTER: 60 * 60, // 1小时
  RATE_LIMIT_PASSWORD_RESET: 60 * 60, // 1小时
  RATE_LIMIT_GENERAL: 15 * 60, // 15分钟
  
  // OAuth
  OAUTH_STATE: 10 * 60, // 10分钟
  OAUTH_CODE: 5 * 60, // 5分钟
  
  // MFA
  MFA_CODE: 5 * 60, // 5分钟
  MFA_BACKUP_CODES: 30 * 24 * 60 * 60, // 30天
  
  // 用户数据缓存
  USER_PERMISSIONS: 30 * 60, // 30分钟
  USER_PROFILE: 15 * 60, // 15分钟
  
  // 系统配置
  SYSTEM_CONFIG: 60 * 60, // 1小时
  OAUTH_PROVIDERS: 60 * 60, // 1小时
  
  // 安全相关
  FAILED_LOGIN_ATTEMPTS: 60 * 60, // 1小时
  SUSPICIOUS_ACTIVITY: 24 * 60 * 60, // 24小时
  
  // 邮件验证
  EMAIL_VERIFICATION: 24 * 60 * 60, // 24小时
  PASSWORD_RESET: 60 * 60, // 1小时
  
  // 统计数据
  STATS_DAILY: 7 * 24 * 60 * 60, // 7天
  
  // 缓存锁
  LOCK: 30 // 30秒
} as const;

/**
 * Redis集群配置（如果使用集群模式）
 */
export const redisClusterConfig = {
  enableOfflineQueue: false,
  redisOptions: {
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0'),
    keyPrefix: process.env.REDIS_KEY_PREFIX || 'idp:'
  },
  clusterRetryDelayOnFailover: 100,
  clusterRetryDelayOnClusterDown: 300,
  clusterMaxRedirections: 6,
  scaleReads: 'slave' as const
};

/**
 * Redis连接健康检查配置
 */
export const redisHealthConfig = {
  checkInterval: 30000, // 30秒检查一次
  timeout: 5000, // 5秒超时
  retryAttempts: 3,
  retryDelay: 1000
};

/**
 * 开发环境Redis配置
 */
export const redisDevConfig: Partial<RedisConfig> = {
  host: 'localhost',
  port: 6379,
  db: 1, // 使用不同的数据库避免冲突
  keyPrefix: 'idp:dev:',
  commandTimeout: 10000
};

/**
 * 测试环境Redis配置
 */
export const redisTestConfig: Partial<RedisConfig> = {
  host: 'localhost',
  port: 6379,
  db: 2, // 测试专用数据库
  keyPrefix: 'idp:test:',
  commandTimeout: 5000
};

/**
 * 生产环境Redis配置
 */
export const redisProdConfig: Partial<RedisConfig> = {
  retryDelayOnFailover: 50,
  maxRetriesPerRequest: 5,
  keepAlive: 60000,
  connectTimeout: 5000,
  commandTimeout: 3000
};

/**
 * 根据环境获取Redis配置
 */
export function getRedisConfig(): RedisConfig {
  const baseConfig = { ...redisConfig };
  
  switch (config.server.nodeEnv) {
    case 'development':
      return { ...baseConfig, ...redisDevConfig };
    case 'test':
      return { ...baseConfig, ...redisTestConfig };
    case 'production':
      return { ...baseConfig, ...redisProdConfig };
    default:
      return baseConfig;
  }
}

/**
 * Redis连接选项
 */
export const redisConnectionOptions = {
  // 连接重试配置
  retryDelayOnFailover: redisConfig.retryDelayOnFailover,
  maxRetriesPerRequest: redisConfig.maxRetriesPerRequest,
  
  // 连接超时配置
  connectTimeout: redisConfig.connectTimeout,
  commandTimeout: redisConfig.commandTimeout,
  
  // 连接保活配置
  keepAlive: redisConfig.keepAlive,
  family: redisConfig.family,
  
  // 懒加载连接
  lazyConnect: redisConfig.lazyConnect,
  
  // 错误处理
  enableReadyCheck: true,
  maxLoadingTimeout: 5000
};
