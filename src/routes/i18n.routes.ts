/**
 * 国际化路由
 * 提供语言管理、翻译资源、本地化配置等API路由
 */

import { Router } from 'express';
import { i18nController } from '@/controllers/i18n.controller';
import { authenticateToken } from '@/middleware/auth.middleware';
import { requireRole } from '@/middleware/rbac.middleware';
import { apiRateLimit } from '@/middleware/security.middleware';
import { responseCache } from '@/middleware/performance.middleware';
import { languageSwitchMiddleware } from '@/middleware/i18n.middleware';

const router = Router();

// 应用限流中间件
router.use(apiRateLimit);

// 语言切换中间件（无需认证）
router.use('/switch', languageSwitchMiddleware());

/**
 * 获取支持的语言列表
 * GET /api/v1/i18n/languages
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "languages": [
 *       {
 *         "code": "zh-CN",
 *         "name": "Chinese (Simplified)",
 *         "nativeName": "简体中文",
 *         "direction": "ltr",
 *         "region": "China",
 *         "flag": "🇨🇳",
 *         "enabled": true,
 *         "completeness": 100,
 *         "isCurrent": true,
 *         "isDefault": false
 *       },
 *       {
 *         "code": "en-US",
 *         "name": "English (US)",
 *         "nativeName": "English",
 *         "direction": "ltr",
 *         "region": "United States",
 *         "flag": "🇺🇸",
 *         "enabled": true,
 *         "completeness": 100,
 *         "isCurrent": false,
 *         "isDefault": true
 *       }
 *     ],
 *     "currentLanguage": "zh-CN",
 *     "totalLanguages": 12,
 *     "enabledLanguages": 12
 *   }
 * }
 */
router.get('/languages', 
  responseCache({ ttl: 3600 }), // 1小时缓存
  i18nController.getSupportedLanguages
);

/**
 * 获取翻译资源
 * GET /api/v1/i18n/translations
 * 
 * 查询参数：
 * - language: 语言代码（可选，默认为当前用户语言）
 * - namespace: 命名空间（可选，默认为'common'）
 * - keys: 翻译键列表（可选，不提供则返回所有翻译）
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "language": "zh-CN",
 *     "namespace": "common",
 *     "translations": {
 *       "auth.login": "登录",
 *       "auth.logout": "登出",
 *       "common.save": "保存",
 *       "common.cancel": "取消"
 *     },
 *     "translationCount": 4
 *   }
 * }
 */
router.get('/translations', 
  responseCache({ ttl: 1800 }), // 30分钟缓存
  i18nController.getTranslations
);

/**
 * 设置用户语言偏好
 * POST /api/v1/i18n/preference
 * 
 * 请求体：
 * {
 *   "language": "zh-CN"
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "语言偏好已更新",
 *   "data": {
 *     "language": "zh-CN",
 *     "languageInfo": {
 *       "code": "zh-CN",
 *       "name": "Chinese (Simplified)",
 *       "nativeName": "简体中文",
 *       "direction": "ltr",
 *       "region": "China",
 *       "flag": "🇨🇳",
 *       "enabled": true,
 *       "completeness": 100
 *     }
 *   }
 * }
 */
router.post('/preference', 
  authenticateToken,
  i18nController.setLanguagePreference
);

/**
 * 获取本地化配置
 * GET /api/v1/i18n/localization
 * 
 * 查询参数：
 * - language: 语言代码（可选，默认为当前用户语言）
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "language": "zh-CN",
 *     "languageInfo": {
 *       "code": "zh-CN",
 *       "name": "Chinese (Simplified)",
 *       "nativeName": "简体中文",
 *       "direction": "ltr",
 *       "region": "China",
 *       "flag": "🇨🇳",
 *       "enabled": true,
 *       "completeness": 100
 *     },
 *     "localization": {
 *       "dateFormat": "YYYY-MM-DD",
 *       "timeFormat": "HH:mm:ss",
 *       "numberFormat": {
 *         "decimal": ".",
 *         "thousands": ",",
 *         "currency": "¥"
 *       },
 *       "timezone": "Asia/Shanghai",
 *       "firstDayOfWeek": 1
 *     },
 *     "examples": {
 *       "date": "2024-01-01",
 *       "time": "12:00:00",
 *       "number": "1,234,567.89",
 *       "currency": "¥1,234.56",
 *       "relativeTime": "2小时前"
 *     }
 *   }
 * }
 */
router.get('/localization', 
  responseCache({ ttl: 3600 }), // 1小时缓存
  i18nController.getLocalizationConfig
);

/**
 * 翻译文本
 * POST /api/v1/i18n/translate
 * 
 * 请求体：
 * {
 *   "key": "auth.login",           // 单个翻译键
 *   "keys": ["auth.login", "auth.logout"], // 或批量翻译键
 *   "language": "zh-CN",          // 可选，默认为当前用户语言
 *   "namespace": "common",        // 可选，默认为'common'
 *   "variables": {                // 可选，变量替换
 *     "username": "张三"
 *   }
 * }
 * 
 * 单个翻译响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "key": "auth.login",
 *     "translation": "登录",
 *     "language": "zh-CN",
 *     "namespace": "common"
 *   }
 * }
 * 
 * 批量翻译响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "keys": ["auth.login", "auth.logout"],
 *     "translations": {
 *       "auth.login": "登录",
 *       "auth.logout": "登出"
 *     },
 *     "language": "zh-CN",
 *     "namespace": "common",
 *     "count": 2
 *   }
 * }
 */
router.post('/translate', 
  i18nController.translateText
);

/**
 * 获取国际化统计信息
 * GET /api/v1/i18n/statistics
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "totalLanguages": 12,
 *     "enabledLanguages": 12,
 *     "disabledLanguages": 0,
 *     "languagesByRegion": {
 *       "China": 2,
 *       "United States": 1,
 *       "United Kingdom": 1,
 *       "Japan": 1,
 *       "South Korea": 1,
 *       "France": 1,
 *       "Germany": 1,
 *       "Spain": 1,
 *       "Brazil": 1,
 *       "Russia": 1,
 *       "Saudi Arabia": 1
 *     },
 *     "completenessStats": {
 *       "fullyTranslated": 2,
 *       "partiallyTranslated": 10,
 *       "notTranslated": 0,
 *       "averageCompleteness": 75.8
 *     },
 *     "directionStats": {
 *       "ltr": 11,
 *       "rtl": 1
 *     }
 *   }
 * }
 */
router.get('/statistics', 
  authenticateToken,
  requireRole(['admin', 'translator']),
  responseCache({ ttl: 1800 }), // 30分钟缓存
  i18nController.getI18nStatistics
);

/**
 * 检测语言
 * POST /api/v1/i18n/detect
 * 
 * 请求体：
 * {
 *   "text": "你好世界",              // 可选，基于文本内容检测
 *   "acceptLanguage": "zh-CN,zh;q=0.9,en;q=0.8", // 可选，HTTP Accept-Language头
 *   "userAgent": "Mozilla/5.0..." // 可选，HTTP User-Agent头
 * }
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "data": {
 *     "detectedLanguage": "zh-CN",
 *     "languageInfo": {
 *       "code": "zh-CN",
 *       "name": "Chinese (Simplified)",
 *       "nativeName": "简体中文",
 *       "direction": "ltr",
 *       "region": "China",
 *       "flag": "🇨🇳",
 *       "enabled": true,
 *       "completeness": 100
 *     },
 *     "confidence": 0.95
 *   }
 * }
 */
router.post('/detect', 
  i18nController.detectLanguage
);

/**
 * 语言切换端点
 * POST /api/v1/i18n/switch
 * 
 * 请求体：
 * {
 *   "language": "zh-CN"
 * }
 * 
 * 或查询参数：
 * GET /api/v1/i18n/switch?lang=zh-CN
 * 
 * 响应格式：
 * {
 *   "success": true,
 *   "message": "Language preference updated",
 *   "language": "zh-CN"
 * }
 */
// 语言切换已在中间件中处理，这里只是文档说明

export default router;
