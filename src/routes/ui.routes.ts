/**
 * 前端UI路由
 * 处理前端界面的路由和静态文件服务
 */

import { Router, Request, Response } from 'express'
import path from 'path'
import fs from 'fs'
import express from 'express'

const router = Router()

// 静态文件目录
const FRONTEND_DIST_PATH = path.join(__dirname, '../../dist/frontend')
const FRONTEND_INDEX_PATH = path.join(FRONTEND_DIST_PATH, 'index.html')

/**
 * 检查前端构建文件是否存在
 */
const checkFrontendBuild = (): boolean => {
  return fs.existsSync(FRONTEND_DIST_PATH) && fs.existsSync(FRONTEND_INDEX_PATH)
}

/**
 * 读取并处理 index.html 模板
 */
const getIndexHtml = (customConfig?: any): string => {
  if (!fs.existsSync(FRONTEND_INDEX_PATH)) {
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>身份提供商 - 前端未构建</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            display: flex; 
            justify-content: center; 
            align-items: center; 
            height: 100vh; 
            margin: 0; 
            background: #f5f5f5;
        }
        .container { 
            text-align: center; 
            padding: 40px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-width: 500px;
        }
        .logo {
            width: 64px;
            height: 64px;
            background: #1890ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
            margin: 0 auto 20px;
        }
        h1 { color: #262626; margin-bottom: 16px; }
        p { color: #8c8c8c; margin-bottom: 24px; }
        .code { 
            background: #f6f8fa; 
            padding: 16px; 
            border-radius: 6px; 
            font-family: monospace;
            text-align: left;
            margin: 16px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">IdP</div>
        <h1>身份提供商</h1>
        <p>前端界面尚未构建，请运行以下命令构建前端：</p>
        <div class="code">
            npm run build:frontend
        </div>
        <p>或者在开发模式下运行：</p>
        <div class="code">
            npm run dev:frontend
        </div>
    </div>
</body>
</html>`
  }

  let html = fs.readFileSync(FRONTEND_INDEX_PATH, 'utf-8')
  
  // 如果有自定义配置，可以在这里注入
  if (customConfig) {
    const configScript = `
      <script>
        window.__APP_CONFIG__ = ${JSON.stringify(customConfig)};
      </script>
    `
    html = html.replace('</head>', `${configScript}</head>`)
  }

  return html
}

/**
 * 获取UI配置
 */
const getUIConfig = (req: Request) => {
  // 从环境变量或数据库获取UI配置
  return {
    title: process.env.UI_TITLE || '身份提供商',
    logo: process.env.UI_LOGO || null,
    theme: {
      primaryColor: process.env.UI_PRIMARY_COLOR || '#1890ff',
      borderRadius: parseInt(process.env.UI_BORDER_RADIUS || '6'),
    },
    features: {
      registration: process.env.ENABLE_REGISTRATION !== 'false',
      socialLogin: process.env.ENABLE_SOCIAL_LOGIN !== 'false',
      mfa: process.env.ENABLE_MFA !== 'false',
    },
    oauth: {
      google: {
        enabled: !!process.env.GOOGLE_CLIENT_ID,
        clientId: process.env.GOOGLE_CLIENT_ID,
      },
      github: {
        enabled: !!process.env.GITHUB_CLIENT_ID,
        clientId: process.env.GITHUB_CLIENT_ID,
      },
    },
  }
}

/**
 * 静态文件服务中间件
 */
if (checkFrontendBuild()) {
  // 如果前端已构建，提供静态文件服务
  router.use('/assets', (req, res, next) => {
    const assetsPath = path.join(FRONTEND_DIST_PATH, 'assets')
    if (fs.existsSync(assetsPath)) {
      return require('express').static(assetsPath)(req, res, next)
    }
    next()
  })
}

/**
 * @route GET /ui/config
 * @desc 获取UI配置
 * @access Public
 */
router.get('/config', (req: Request, res: Response) => {
  try {
    const config = getUIConfig(req)
    res.json(config)
  } catch (error) {
    res.status(500).json({
      error: 'config_error',
      message: '获取UI配置失败'
    })
  }
})

/**
 * @route GET /login, /register, /mfa, etc.
 * @desc 前端路由处理 - SPA路由
 * @access Public
 */
const frontendRoutes = [
  '/login',
  '/register',
  '/mfa',
  '/forgot-password',
  '/reset-password',
  '/dashboard',
  '/profile',
  '/settings',
  '/oauth/callback',
  '/demo',
]

frontendRoutes.forEach(route => {
  router.get(route, (req: Request, res: Response) => {
    try {
      const config = getUIConfig(req)
      const html = getIndexHtml(config)
      res.setHeader('Content-Type', 'text/html')
      res.send(html)
    } catch (error) {
      res.status(500).send('服务器内部错误')
    }
  })
})

/**
 * @route GET /
 * @desc 根路径 - 重定向到登录页面或仪表板
 * @access Public
 */
router.get('/', (req: Request, res: Response) => {
  // 检查用户是否已认证
  const authHeader = req.headers.authorization
  const hasToken = authHeader && authHeader.startsWith('Bearer ')

  if (hasToken) {
    // 如果有令牌，重定向到仪表板
    res.redirect('/dashboard')
  } else {
    // 否则重定向到演示页面
    res.redirect('/demo')
  }
})

/**
 * @route GET /health/ui
 * @desc UI健康检查
 * @access Public
 */
router.get('/health', (req: Request, res: Response) => {
  const frontendBuilt = checkFrontendBuild()
  
  res.json({
    status: 'ok',
    frontend: {
      built: frontendBuilt,
      path: FRONTEND_DIST_PATH,
    },
    timestamp: new Date().toISOString(),
  })
})

/**
 * Catch-all handler for SPA routing
 * 必须放在最后，处理所有未匹配的路由
 */
router.get('*', (req: Request, res: Response) => {
  // 跳过API路由
  if (req.path.startsWith('/api/') || req.path.startsWith('/nsa/')) {
    return res.status(404).json({
      error: 'not_found',
      message: '请求的资源不存在'
    });
  }

  try {
    const config = getUIConfig(req);
    const html = getIndexHtml(config);
    res.setHeader('Content-Type', 'text/html');
    return res.send(html);
  } catch (error) {
    return res.status(500).send('服务器内部错误');
  }
})

// 静态文件服务
const publicPath = path.join(__dirname, '../../public')
router.use('/public', express.static(publicPath))

// 管理员控制台静态文件服务
const adminPublicPath = path.join(publicPath, 'admin')
router.use('/admin', express.static(adminPublicPath))

/**
 * 登录页面
 * GET /login
 */
router.get('/login', (req: Request, res: Response) => {
  const loginPath = path.join(publicPath, 'login.html')

  if (fs.existsSync(loginPath)) {
    res.sendFile(loginPath)
  } else {
    res.status(404).send('登录页面未找到')
  }
})

/**
 * 管理员控制台主页
 * GET /admin
 */
router.get('/admin', (req: Request, res: Response) => {
  const adminIndexPath = path.join(adminPublicPath, 'index.html')

  if (fs.existsSync(adminIndexPath)) {
    res.sendFile(adminIndexPath)
  } else {
    res.status(404).send(`
      <!DOCTYPE html>
      <html lang="zh-CN">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>管理员控制台 - 文件未找到</title>
          <style>
              body {
                  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  height: 100vh;
                  margin: 0;
                  background: #f5f5f5;
              }
              .container {
                  text-align: center;
                  padding: 40px;
                  background: white;
                  border-radius: 8px;
                  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                  max-width: 500px;
              }
              .icon { font-size: 48px; margin-bottom: 20px; }
              h1 { color: #333; margin-bottom: 10px; }
              p { color: #666; line-height: 1.6; }
              .btn {
                  display: inline-block;
                  padding: 12px 24px;
                  background: #007bff;
                  color: white;
                  text-decoration: none;
                  border-radius: 4px;
                  margin-top: 20px;
              }
          </style>
      </head>
      <body>
          <div class="container">
              <div class="icon">🔧</div>
              <h1>管理员控制台文件未找到</h1>
              <p>管理员控制台的静态文件不存在。请检查 public/admin 目录是否包含必要的文件。</p>
              <a href="/" class="btn">返回首页</a>
          </div>
      </body>
      </html>
    `)
  }
})

/**
 * 管理员控制台SPA路由处理
 * 处理管理员控制台的单页应用路由
 */
router.get('/admin/*', (req: Request, res: Response) => {
  const adminIndexPath = path.join(adminPublicPath, 'index.html')

  if (fs.existsSync(adminIndexPath)) {
    res.sendFile(adminIndexPath)
  } else {
    res.redirect('/admin')
  }
})

export default router
