/**
 * 国际化控制器
 * 提供语言管理、翻译资源、本地化配置等API接口
 */

import { Request, Response } from 'express';
import { logger } from '@/config/logger';
import { i18nService, SupportedLanguage } from '@/services/i18n.service';
import { securityAuditService, AuditEventType, AuditSeverity } from '@/services/security-audit.service';
import { cacheService } from '@/services/cache.service';

/**
 * 国际化控制器
 */
export class I18nController {

  /**
   * 获取支持的语言列表
   * GET /api/v1/i18n/languages
   */
  getSupportedLanguages = async (req: Request, res: Response): Promise<void> => {
    try {
      const languages = i18nService.getSupportedLanguages();
      
      // 添加当前用户的语言偏好信息
      const currentLanguage = req.i18n?.language || SupportedLanguage.EN_US;
      
      const languagesWithStatus = languages.map(lang => ({
        ...lang,
        isCurrent: lang.code === currentLanguage,
        isDefault: lang.code === SupportedLanguage.EN_US
      }));

      res.json({
        success: true,
        data: {
          languages: languagesWithStatus,
          currentLanguage,
          totalLanguages: languages.length,
          enabledLanguages: languages.filter(l => l.enabled).length
        }
      });

    } catch (error) {
      logger.error('获取支持语言列表失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取支持语言列表失败'
      });
    }
  };

  /**
   * 获取翻译资源
   * GET /api/v1/i18n/translations
   */
  getTranslations = async (req: Request, res: Response): Promise<void> => {
    try {
      const { 
        language = req.i18n?.language || SupportedLanguage.EN_US,
        namespace = 'common',
        keys 
      } = req.query;

      const lang = language as SupportedLanguage;
      
      // 验证语言是否支持
      const languageInfo = i18nService.getLanguageInfo(lang);
      if (!languageInfo) {
        return res.status(400).json({
          success: false,
          error: 'UNSUPPORTED_LANGUAGE',
          message: `不支持的语言: ${lang}`
        });
      }

      let translations: Record<string, string>;

      if (keys) {
        // 获取指定键的翻译
        const keyList = Array.isArray(keys) ? keys as string[] : [keys as string];
        translations = i18nService.translateBatch(keyList, {
          language: lang,
          namespace: namespace as string
        });
      } else {
        // 获取所有翻译（这里需要实现获取所有翻译的方法）
        translations = await this.getAllTranslations(lang, namespace as string);
      }

      res.json({
        success: true,
        data: {
          language: lang,
          namespace,
          translations,
          translationCount: Object.keys(translations).length
        }
      });

    } catch (error) {
      logger.error('获取翻译资源失败', {
        error: error instanceof Error ? error.message : String(error),
        language: req.query.language,
        namespace: req.query.namespace
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取翻译资源失败'
      });
    }
  };

  /**
   * 设置用户语言偏好
   * POST /api/v1/i18n/preference
   */
  setLanguagePreference = async (req: Request, res: Response): Promise<void> => {
    try {
      const { language } = req.body;
      const userId = req.user?.id;

      if (!language) {
        return res.status(400).json({
          success: false,
          error: 'LANGUAGE_REQUIRED',
          message: '语言参数是必需的'
        });
      }

      // 验证语言是否支持
      if (!Object.values(SupportedLanguage).includes(language as SupportedLanguage)) {
        return res.status(400).json({
          success: false,
          error: 'UNSUPPORTED_LANGUAGE',
          message: `不支持的语言: ${language}`
        });
      }

      const lang = language as SupportedLanguage;
      const languageInfo = i18nService.getLanguageInfo(lang);
      
      if (!languageInfo?.enabled) {
        return res.status(400).json({
          success: false,
          error: 'LANGUAGE_DISABLED',
          message: `语言已禁用: ${language}`
        });
      }

      // 更新会话
      if (req.session) {
        req.session.language = lang;
      }

      // 更新用户偏好缓存
      if (userId) {
        const cacheKey = `user_language:${userId}`;
        await cacheService.set(cacheKey, lang, 30 * 24 * 60 * 60); // 30天

        // 这里可以更新数据库中的用户偏好
        // await updateUserLanguagePreference(userId, lang);
      }

      // 记录审计事件
      await securityAuditService.logAuditEvent({
        eventType: AuditEventType.USER_PROFILE_UPDATE,
        severity: AuditSeverity.LOW,
        userId,
        sessionId: req.sessionID,
        ipAddress: req.ip || 'unknown',
        userAgent: req.get('User-Agent'),
        resource: 'user_language_preference',
        action: 'update_language',
        details: {
          newLanguage: lang,
          previousLanguage: req.i18n?.language
        },
        success: true
      });

      // 设置Cookie
      res.cookie('i18n_lang', lang, {
        maxAge: 365 * 24 * 60 * 60 * 1000, // 1年
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax'
      });

      res.json({
        success: true,
        message: req.i18n?.t('i18n.preference_updated') || 'Language preference updated',
        data: {
          language: lang,
          languageInfo
        }
      });

    } catch (error) {
      logger.error('设置语言偏好失败', {
        error: error instanceof Error ? error.message : String(error),
        userId: req.user?.id,
        language: req.body.language
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '设置语言偏好失败'
      });
    }
  };

  /**
   * 获取本地化配置
   * GET /api/v1/i18n/localization
   */
  getLocalizationConfig = async (req: Request, res: Response): Promise<void> => {
    try {
      const { language = req.i18n?.language || SupportedLanguage.EN_US } = req.query;
      const lang = language as SupportedLanguage;

      // 验证语言是否支持
      const languageInfo = i18nService.getLanguageInfo(lang);
      if (!languageInfo) {
        return res.status(400).json({
          success: false,
          error: 'UNSUPPORTED_LANGUAGE',
          message: `不支持的语言: ${lang}`
        });
      }

      const localizationConfig = i18nService.getLocalizationConfig(lang);

      res.json({
        success: true,
        data: {
          language: lang,
          languageInfo,
          localization: localizationConfig,
          examples: {
            date: i18nService.formatDate(new Date(), lang),
            time: i18nService.formatTime(new Date(), lang),
            number: i18nService.formatNumber(1234567.89, lang),
            currency: i18nService.formatCurrency(1234.56, localizationConfig.numberFormat.currency, lang),
            relativeTime: i18nService.getRelativeTime(new Date(Date.now() - 2 * 60 * 60 * 1000), lang) // 2小时前
          }
        }
      });

    } catch (error) {
      logger.error('获取本地化配置失败', {
        error: error instanceof Error ? error.message : String(error),
        language: req.query.language
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取本地化配置失败'
      });
    }
  };

  /**
   * 翻译文本
   * POST /api/v1/i18n/translate
   */
  translateText = async (req: Request, res: Response): Promise<void> => {
    try {
      const {
        key,
        keys,
        language = req.i18n?.language || SupportedLanguage.EN_US,
        namespace = 'common',
        variables = {}
      } = req.body;

      if (!key && !keys) {
        return res.status(400).json({
          success: false,
          error: 'KEY_REQUIRED',
          message: '翻译键是必需的'
        });
      }

      const lang = language as SupportedLanguage;

      let result: any;

      if (key) {
        // 单个翻译
        const translation = i18nService.translate(key, {
          language: lang,
          namespace,
          variables
        });

        result = {
          key,
          translation,
          language: lang,
          namespace
        };
      } else {
        // 批量翻译
        const translations = i18nService.translateBatch(keys, {
          language: lang,
          namespace,
          variables
        });

        result = {
          keys,
          translations,
          language: lang,
          namespace,
          count: Object.keys(translations).length
        };
      }

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      logger.error('翻译文本失败', {
        error: error instanceof Error ? error.message : String(error),
        key: req.body.key,
        language: req.body.language
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '翻译文本失败'
      });
    }
  };

  /**
   * 获取国际化统计信息
   * GET /api/v1/i18n/statistics
   */
  getI18nStatistics = async (req: Request, res: Response): Promise<void> => {
    try {
      const languages = i18nService.getSupportedLanguages();
      
      // 计算统计信息
      const statistics = {
        totalLanguages: languages.length,
        enabledLanguages: languages.filter(l => l.enabled).length,
        disabledLanguages: languages.filter(l => !l.enabled).length,
        languagesByRegion: this.groupLanguagesByRegion(languages),
        completenessStats: {
          fullyTranslated: languages.filter(l => l.completeness === 100).length,
          partiallyTranslated: languages.filter(l => l.completeness > 0 && l.completeness < 100).length,
          notTranslated: languages.filter(l => l.completeness === 0).length,
          averageCompleteness: languages.reduce((sum, l) => sum + l.completeness, 0) / languages.length
        },
        directionStats: {
          ltr: languages.filter(l => l.direction === 'ltr').length,
          rtl: languages.filter(l => l.direction === 'rtl').length
        }
      };

      res.json({
        success: true,
        data: statistics
      });

    } catch (error) {
      logger.error('获取国际化统计失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '获取国际化统计失败'
      });
    }
  };

  /**
   * 检测语言
   * POST /api/v1/i18n/detect
   */
  detectLanguage = async (req: Request, res: Response): Promise<void> => {
    try {
      const { text, acceptLanguage, userAgent } = req.body;

      let detectedLanguage: SupportedLanguage;

      if (text) {
        // 基于文本内容检测语言（这里需要实现文本语言检测）
        detectedLanguage = this.detectLanguageFromText(text);
      } else {
        // 基于HTTP头部检测语言
        detectedLanguage = i18nService.detectUserLanguage(
          acceptLanguage || req.get('Accept-Language'),
          userAgent || req.get('User-Agent')
        );
      }

      const languageInfo = i18nService.getLanguageInfo(detectedLanguage);

      res.json({
        success: true,
        data: {
          detectedLanguage,
          languageInfo,
          confidence: text ? this.calculateDetectionConfidence(text, detectedLanguage) : 0.8
        }
      });

    } catch (error) {
      logger.error('语言检测失败', {
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: 'INTERNAL_ERROR',
        message: '语言检测失败'
      });
    }
  };

  /**
   * 获取所有翻译（私有方法）
   */
  private async getAllTranslations(language: SupportedLanguage, namespace: string): Promise<Record<string, string>> {
    // 这里应该实现从翻译服务获取所有翻译的逻辑
    // 目前返回一些示例翻译
    const commonTranslations = {
      'auth.login': i18nService.translate('auth.login', { language, namespace }),
      'auth.logout': i18nService.translate('auth.logout', { language, namespace }),
      'auth.register': i18nService.translate('auth.register', { language, namespace }),
      'common.save': i18nService.translate('common.save', { language, namespace }),
      'common.cancel': i18nService.translate('common.cancel', { language, namespace }),
      'common.delete': i18nService.translate('common.delete', { language, namespace }),
      'common.edit': i18nService.translate('common.edit', { language, namespace }),
      'common.loading': i18nService.translate('common.loading', { language, namespace }),
      'common.error': i18nService.translate('common.error', { language, namespace }),
      'common.success': i18nService.translate('common.success', { language, namespace })
    };

    return commonTranslations;
  }

  /**
   * 按地区分组语言
   */
  private groupLanguagesByRegion(languages: any[]): Record<string, number> {
    const regionGroups: Record<string, number> = {};
    
    for (const lang of languages) {
      const region = lang.region || 'Unknown';
      regionGroups[region] = (regionGroups[region] || 0) + 1;
    }
    
    return regionGroups;
  }

  /**
   * 从文本检测语言（简化实现）
   */
  private detectLanguageFromText(text: string): SupportedLanguage {
    // 简化的文本语言检测
    const chinesePattern = /[\u4e00-\u9fff]/;
    const japanesePattern = /[\u3040-\u309f\u30a0-\u30ff]/;
    const koreanPattern = /[\uac00-\ud7af]/;
    const arabicPattern = /[\u0600-\u06ff]/;
    const russianPattern = /[\u0400-\u04ff]/;

    if (chinesePattern.test(text)) {
      return SupportedLanguage.ZH_CN;
    } else if (japanesePattern.test(text)) {
      return SupportedLanguage.JA_JP;
    } else if (koreanPattern.test(text)) {
      return SupportedLanguage.KO_KR;
    } else if (arabicPattern.test(text)) {
      return SupportedLanguage.AR_SA;
    } else if (russianPattern.test(text)) {
      return SupportedLanguage.RU_RU;
    } else {
      return SupportedLanguage.EN_US;
    }
  }

  /**
   * 计算检测置信度
   */
  private calculateDetectionConfidence(text: string, detectedLanguage: SupportedLanguage): number {
    // 简化的置信度计算
    const textLength = text.length;
    if (textLength < 10) return 0.5;
    if (textLength < 50) return 0.7;
    if (textLength < 100) return 0.8;
    return 0.9;
  }
}

// 创建控制器实例
export const i18nController = new I18nController();
