import React from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useAuthStore } from '../stores/authStore'

interface ProtectedRouteProps {
  children: React.ReactNode
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, isLoading, mfaRequired } = useAuthStore()
  const location = useLocation()

  // 如果正在加载，显示加载状态
  if (isLoading) {
    return (
      <div className="loading-container">
        <div>验证身份中...</div>
      </div>
    )
  }

  // 如果需要 MFA 验证，重定向到 MFA 页面
  if (mfaRequired) {
    return <Navigate to="/mfa" state={{ from: location }} replace />
  }

  // 如果未认证，重定向到登录页面
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  // 已认证，渲染子组件
  return <>{children}</>
}

export default ProtectedRoute
